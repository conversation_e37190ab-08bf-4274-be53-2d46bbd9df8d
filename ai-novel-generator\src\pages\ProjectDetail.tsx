import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, Trash2, Plus, BookOpen, FileText, Calendar, Wand2 } from 'lucide-react';
import { useProjectStore } from '../stores/useProjectStore';
import OutlineGenerator from '../components/OutlineGenerator';
import ChapterGenerator from '../components/ChapterGenerator';

const ProjectDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { projects, currentProject, setCurrentProject, deleteProject } = useProjectStore();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showOutlineGenerator, setShowOutlineGenerator] = useState(false);
  const [showChapterGenerator, setShowChapterGenerator] = useState(false);

  useEffect(() => {
    if (id) {
      setCurrentProject(id);
    }
  }, [id, setCurrentProject]);

  const project = currentProject || projects.find(p => p.id === id);

  if (!project) {
    return (
      <div className="text-center py-12">
        <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">项目不存在</h3>
        <button
          onClick={() => navigate('/')}
          className="text-blue-600 hover:text-blue-800"
        >
          返回项目列表
        </button>
      </div>
    );
  }

  const handleDelete = () => {
    if (project) {
      deleteProject(project.id);
      navigate('/');
    }
  };

  const getTotalWordCount = () => {
    return project.chapters.reduce((total, chapter) => total + chapter.wordCount, 0);
  };

  return (
    <div className="space-y-6">
      {/* 头部导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/')}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{project.title}</h1>
            <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                {project.genre}
              </span>
              <span>{project.theme}</span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
            <Edit className="h-5 w-5" />
          </button>
          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
          >
            <Trash2 className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* 项目统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium text-gray-600">总字数</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">{getTotalWordCount()}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center space-x-2">
            <BookOpen className="h-5 w-5 text-green-600" />
            <span className="text-sm font-medium text-gray-600">章节数</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">{project.chapters.length}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-purple-600" />
            <span className="text-sm font-medium text-gray-600">创建时间</span>
          </div>
          <p className="text-sm font-semibold text-gray-900 mt-1">
            {project.createdAt.toLocaleDateString()}
          </p>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-orange-600" />
            <span className="text-sm font-medium text-gray-600">最后更新</span>
          </div>
          <p className="text-sm font-semibold text-gray-900 mt-1">
            {project.updatedAt.toLocaleDateString()}
          </p>
        </div>
      </div>

      {/* 大纲部分 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">大纲</h2>
          {!project.outline && (
            <button
              onClick={() => setShowOutlineGenerator(true)}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
            >
              <Wand2 className="h-4 w-4" />
              <span>AI生成大纲</span>
            </button>
          )}
        </div>

        {project.outline ? (
          <div className="prose max-w-none">
            <p className="text-gray-700 whitespace-pre-wrap">{project.outline}</p>
          </div>
        ) : (
          <div className="text-center py-8">
            <Wand2 className="h-12 w-12 text-purple-400 mx-auto mb-3" />
            <p className="text-gray-600 mb-4">还没有大纲</p>
            <p className="text-sm text-gray-500">使用AI生成功能快速创建故事大纲</p>
          </div>
        )}
      </div>

      {/* 章节列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">章节</h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setShowChapterGenerator(true)}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
            >
              <Wand2 className="h-4 w-4" />
              <span>AI生成章节</span>
            </button>
            <button
              onClick={() => navigate(`/project/${project.id}/chapter/new`)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>手动创建</span>
            </button>
          </div>
        </div>
        
        {project.chapters.length === 0 ? (
          <div className="text-center py-12">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-600 mb-4">还没有章节</p>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowChapterGenerator(true)}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
              >
                <Wand2 className="h-4 w-4" />
                <span>AI生成第一章</span>
              </button>
              <button
                onClick={() => navigate(`/project/${project.id}/chapter/new`)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                手动创建第一章
              </button>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {project.chapters.map((chapter, index) => (
              <div key={chapter.id} className="p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">
                      第{index + 1}章 {chapter.title}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                      <span>{chapter.wordCount} 字</span>
                      <span>{chapter.updatedAt.toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => navigate(`/project/${project.id}/chapter/${chapter.id}`)}
                      className="p-1 text-gray-600 hover:text-gray-900"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="p-1 text-red-600 hover:text-red-700">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">确认删除</h3>
              <p className="text-gray-600 mb-6">
                确定要删除项目 "{project.title}" 吗？此操作无法撤销。
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* AI生成模态框 */}
      {showOutlineGenerator && (
        <OutlineGenerator
          projectId={project.id}
          onClose={() => setShowOutlineGenerator(false)}
        />
      )}

      {showChapterGenerator && (
        <ChapterGenerator
          projectId={project.id}
          onClose={() => setShowChapterGenerator(false)}
        />
      )}
    </div>
  );
};

export default ProjectDetail;
